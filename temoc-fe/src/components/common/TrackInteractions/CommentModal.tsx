'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { FaTimes, FaHeart, FaRegHeart, FaReply, FaEdit, FaTrash, FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { BsEmojiSmile } from 'react-icons/bs';
import { trackInteractionService, Comment, CreateCommentData } from '@/services/track-interaction.service';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'react-toastify';
import Image from 'next/image';
// import EmojiPicker from 'emoji-picker-react';

interface CommentModalProps {
  isOpen: boolean;
  onClose: () => void;
  trackId: string;
  trackTitle: string;
}

interface CommentItemProps {
  comment: Comment;
  trackId: string;
  onReply: (commentId: string) => void;
  onEdit: (commentId: string, content: string) => void;
  onDelete: (commentId: string) => void;
  onLike: (commentId: string) => void;
  currentUserId?: string;
  level?: number;
  refreshTrigger?: number; // Add trigger to force refresh
}

const CommentItem: React.FC<CommentItemProps> = ({
  comment,
  trackId,
  onReply,
  onEdit,
  onDelete,
  onLike,
  currentUserId,
  level = 0,
  refreshTrigger = 0,
}) => {
  const [showReplies, setShowReplies] = useState(false);
  const [replies, setReplies] = useState<Comment[]>([]);
  const [loadingReplies, setLoadingReplies] = useState(false);
  const queryClient = useQueryClient();

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return 'just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    return `${Math.floor(diffInSeconds / 86400)}d ago`;
  };

  const loadReplies = async (forceRefresh = false) => {
    if (loadingReplies && !forceRefresh) return;

    console.log(`[DEBUG FRONTEND] Loading replies for comment ${comment._id}, forceRefresh: ${forceRefresh}`);
    setLoadingReplies(true);
    try {
      const repliesData = await trackInteractionService.getComments(trackId, 1, 50, comment._id);
      console.log(`[DEBUG FRONTEND] Received ${repliesData.comments.length} replies for comment ${comment._id}:`, repliesData.comments);
      setReplies(repliesData.comments);
      setShowReplies(true);

      // Update the comment's reply count if it has changed
      if (comment.replyCount !== repliesData.comments.length) {
        console.log(`[DEBUG FRONTEND] Updating reply count from ${comment.replyCount} to ${repliesData.comments.length}`);
        comment.replyCount = repliesData.comments.length;
      }
    } catch (error) {
      console.error('Failed to load replies:', error);
    } finally {
      setLoadingReplies(false);
    }
  };

  // Listen for comment updates to refresh replies
  React.useEffect(() => {
    const handleCommentUpdate = () => {
      if (showReplies) {
        loadReplies(true); // Force refresh replies when comments are updated
      }
    };

    // Listen for query invalidation
    const unsubscribe = queryClient.getQueryCache().subscribe((event) => {
      if (event?.query?.queryKey?.[0] === 'track-comments' &&
          event?.query?.queryKey?.[1] === trackId &&
          event.type === 'updated') {
        handleCommentUpdate();
      }
    });

    return unsubscribe;
  }, [showReplies, trackId, queryClient]);

  // Refresh replies when refreshTrigger changes
  React.useEffect(() => {
    if (refreshTrigger > 0) {
      // If this comment has replies, refresh them
      if (comment.replyCount && comment.replyCount > 0) {
        if (showReplies) {
          loadReplies(true); // Refresh if already showing
        } else {
          // Auto-expand if new replies were added
          loadReplies(false);
        }
      }
    }
  }, [refreshTrigger]);

  const toggleReplies = () => {
    if (!showReplies && replies.length === 0) {
      loadReplies();
    } else {
      setShowReplies(!showReplies);
    }
  };

  // const marginLeft = level * 20; // Indent replies

  return (
    <div className={`${level > 0 ? 'ml-8 mt-3' : ''}`}>
      <div className="flex items-start gap-3">
        <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
          {comment.user.avatarUrl ? (
            <Image
              src={comment.user.avatarUrl}
              alt={comment.user.displayName}
              width={32}
              height={32}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-primary text-white text-sm font-semibold">
              {comment.user.displayName.charAt(0).toUpperCase()}
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-semibold text-sm">{comment.user.displayName}</span>
            <span className="text-xs text-gray-500">
              {formatTimeAgo(comment.createdAt)}
            </span>
            {comment.isEdited && (
              <span className="text-xs text-gray-400">(edited)</span>
            )}
          </div>

          <p className="text-sm text-gray-800 mb-2">{comment.content}</p>

          <div className="flex items-center gap-4 text-xs">
            <button
              onClick={() => onLike(comment._id)}
              className={`flex items-center gap-1 transition-colors ${
                comment.likes.includes(currentUserId || '')
                  ? 'text-red-500'
                  : 'text-gray-500 hover:text-red-500'
              }`}
            >
              {comment.likes.includes(currentUserId || '') ? (
                <FaHeart />
              ) : (
                <FaRegHeart />
              )}
              {comment.likes.length > 0 && comment.likes.length}
            </button>

            <button
              onClick={() => onReply(comment._id)}
              className="flex items-center gap-1 text-gray-500 hover:text-primary transition-colors"
            >
              <FaReply />
              Reply
            </button>

            {comment.user._id === currentUserId && (
              <>
                <button
                  onClick={() => onEdit(comment._id, comment.content)}
                  className="flex items-center gap-1 text-gray-500 hover:text-blue-500 transition-colors"
                >
                  <FaEdit />
                  Edit
                </button>

                <button
                  onClick={() => onDelete(comment._id)}
                  className="flex items-center gap-1 text-gray-500 hover:text-red-500 transition-colors"
                >
                  <FaTrash />
                  Delete
                </button>
              </>
            )}

            {/* Show replies button */}
            {(comment.replyCount || 0) > 0 && (
              <button
                onClick={toggleReplies}
                className="flex items-center gap-1 text-gray-500 hover:text-primary transition-colors"
                disabled={loadingReplies}
              >
                {showReplies ? <FaChevronUp /> : <FaChevronDown />}
                {loadingReplies ? 'Loading...' : `${comment.replyCount} ${comment.replyCount === 1 ? 'reply' : 'replies'}`}
              </button>
            )}
          </div>

          {/* Replies */}
          {showReplies && replies.length > 0 && (
            <div className="mt-3">
              {replies.map((reply) => (
                <CommentItem
                  key={reply._id}
                  comment={reply}
                  trackId={trackId}
                  onReply={onReply}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onLike={onLike}
                  currentUserId={currentUserId}
                  level={level + 1}
                  refreshTrigger={refreshTrigger}
                />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const CommentModal: React.FC<CommentModalProps> = ({
  isOpen,
  onClose,
  trackId,
  trackTitle,
}) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const [mentionQuery, setMentionQuery] = useState('');
  const [showMentions, setShowMentions] = useState(false);
  const [expandedComments, setExpandedComments] = useState<Set<string>>(new Set());
  const [repliesData, setRepliesData] = useState<{ [key: string]: Comment[] }>({});
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  // const replyTextareaRef = useRef<HTMLTextAreaElement>(null);

  // Get comments
  const { data: commentsData, isLoading } = useQuery({
    queryKey: ['track-comments', trackId],
    queryFn: () => {
      console.log(`[DEBUG FRONTEND] Fetching main comments for track ${trackId}`);
      return trackInteractionService.getComments(trackId, 1, 50);
    },
    enabled: isOpen,
  });

  // Log comments data when it changes
  useEffect(() => {
    if (commentsData) {
      console.log(`[DEBUG FRONTEND] Received ${commentsData.comments.length} main comments:`, commentsData.comments);
    }
  }, [commentsData]);

  // Auto-focus textarea when replying
  useEffect(() => {
    if (replyingTo && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [replyingTo]);

  // Create comment mutation
  const createCommentMutation = useMutation({
    mutationFn: (data: CreateCommentData) => trackInteractionService.createComment(trackId, data),
    onSuccess: (newComment) => {
      setNewComment('');
      setReplyingTo(null);
      queryClient.invalidateQueries({ queryKey: ['track-comments', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-stats', trackId] });

      // Trigger refresh for nested replies
      setRefreshTrigger(prev => prev + 1);

      // Show appropriate success message
      if (newComment.parentComment) {
        toast.success('Reply added successfully!');
      } else {
        toast.success('Comment added successfully!');
      }
    },
    onError: () => {
      toast.error('Failed to add comment');
    },
  });

  // Update comment mutation
  const updateCommentMutation = useMutation({
    mutationFn: ({ commentId, data }: { commentId: string; data: { content: string } }) =>
      trackInteractionService.updateComment(commentId, data),
    onSuccess: () => {
      setEditingComment(null);
      setEditContent('');
      queryClient.invalidateQueries({ queryKey: ['track-comments', trackId] });
      toast.success('Comment updated successfully!');
    },
    onError: () => {
      toast.error('Failed to update comment');
    },
  });

  // Delete comment mutation
  const deleteCommentMutation = useMutation({
    mutationFn: (commentId: string) => trackInteractionService.deleteComment(commentId),
    onSuccess: () => {
      // Invalidate all related queries to ensure count updates
      queryClient.invalidateQueries({ queryKey: ['track-comments', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-stats', trackId] });
      queryClient.invalidateQueries({ queryKey: ['track-stats-bulk'] });

      // Force refresh of nested replies
      setRefreshTrigger(prev => prev + 1);

      toast.success('Comment deleted successfully!');
    },
    onError: () => {
      toast.error('Failed to delete comment');
    },
  });

  // Like comment mutation
  const likeCommentMutation = useMutation({
    mutationFn: (commentId: string) => trackInteractionService.toggleCommentLike(commentId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['track-comments', trackId] });
    },
    onError: () => {
      toast.error('Failed to update like status');
    },
  });

  const handleSubmitComment = () => {
    if (!newComment.trim()) return;

    // Extract mentions from comment content
    const mentionRegex = /@(\w+)/g;
    const mentions: string[] = [];
    let match;
    while ((match = mentionRegex.exec(newComment)) !== null) {
      const username = match[1];
      // Find user ID by username from comments data
      const mentionedUser = commentsData?.comments
        .map(c => c.user)
        .find(u => u.username === username);
      if (mentionedUser) {
        mentions.push(mentionedUser._id);
      }
    }

    const commentData: CreateCommentData = {
      content: newComment,
      parentComment: replyingTo || undefined,
      mentions: mentions.length > 0 ? mentions : undefined,
    };

    console.log(`[DEBUG FRONTEND] Submitting comment:`, commentData);
    if (replyingTo) {
      console.log(`[DEBUG FRONTEND] This is a reply to comment: ${replyingTo}`);
    }
    if (mentions.length > 0) {
      console.log(`[DEBUG FRONTEND] Comment includes mentions:`, mentions);
    }

    createCommentMutation.mutate(commentData);
  };

  const handleUpdateComment = (commentId: string) => {
    if (!editContent.trim()) return;

    updateCommentMutation.mutate({
      commentId,
      data: { content: editContent },
    });
  };

  // Load replies for a comment
  const loadReplies = async (commentId: string) => {
    try {
      const replies = await trackInteractionService.getComments(trackId, 1, 50, commentId);
      setRepliesData(prev => ({
        ...prev,
        [commentId]: replies.comments
      }));
    } catch (error) {
      console.error('Failed to load replies:', error);
    }
  };

  // Toggle expand/collapse replies
  // const toggleReplies = (commentId: string) => {
  //   const newExpanded = new Set(expandedComments);
  //   if (newExpanded.has(commentId)) {
  //     newExpanded.delete(commentId);
  //   } else {
  //     newExpanded.add(commentId);
  //     // Load replies if not already loaded
  //     if (!repliesData[commentId]) {
  //       loadReplies(commentId);
  //     }
  //   }
  //   setExpandedComments(newExpanded);
  // };

  const handleEmojiClick = (emoji: string) => {
    setNewComment(prev => prev + emoji);
    setShowEmojiPicker(false);
  };

  // Handle mention detection
  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setNewComment(value);

    // Check for @ mentions
    const lastAtIndex = value.lastIndexOf('@');
    if (lastAtIndex !== -1) {
      const textAfterAt = value.substring(lastAtIndex + 1);
      const spaceIndex = textAfterAt.indexOf(' ');
      const mentionText = spaceIndex === -1 ? textAfterAt : textAfterAt.substring(0, spaceIndex);

      if (mentionText.length > 0 && spaceIndex === -1) {
        setMentionQuery(mentionText);
        setShowMentions(true);
      } else {
        setShowMentions(false);
        setMentionQuery('');
      }
    } else {
      setShowMentions(false);
      setMentionQuery('');
    }
  };

  // Handle mention selection
  const handleMentionSelect = (username: string) => {
    const lastAtIndex = newComment.lastIndexOf('@');
    if (lastAtIndex !== -1) {
      const beforeAt = newComment.substring(0, lastAtIndex);
      const newValue = beforeAt + `@${username} `;
      setNewComment(newValue);
    }
    setShowMentions(false);
    setMentionQuery('');
  };

  // const formatTimeAgo = (dateString: string) => {
  //   const date = new Date(dateString);
  //   const now = new Date();
  //   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  //   if (diffInSeconds < 60) return 'just now';
  //   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  //   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  //   return `${Math.floor(diffInSeconds / 86400)}d ago`;
  // };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-2xl max-h-[90vh] bg-white rounded-lg shadow-xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="text-lg font-semibold">Comments on "{trackTitle}"</h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-500 hover:text-gray-700 transition-colors"
          >
            <FaTimes />
          </button>
        </div>

        {/* Comments List */}
        <div className="flex-1 overflow-y-auto max-h-96 p-4">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : commentsData?.comments.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No comments yet. Be the first to comment!</p>
            </div>
          ) : (
            <div className="space-y-4">
              {commentsData?.comments.map((comment: Comment) => (
                <div key={comment._id} className="border-b pb-4 last:border-b-0">
                  {editingComment === comment._id ? (
                    <div className="space-y-2">
                      <textarea
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                        className="w-full p-2 border rounded-md resize-none"
                        rows={2}
                        placeholder="Edit your comment..."
                      />
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleUpdateComment(comment._id)}
                          disabled={updateCommentMutation.isPending}
                          className="px-3 py-1 bg-primary text-white rounded text-sm hover:bg-orange-600 disabled:opacity-50"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => {
                            setEditingComment(null);
                            setEditContent('');
                          }}
                          className="px-3 py-1 bg-gray-300 text-gray-700 rounded text-sm hover:bg-gray-400"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <CommentItem
                      comment={comment}
                      trackId={trackId}
                      onReply={(commentId) => setReplyingTo(commentId)}
                      onEdit={(commentId, content) => {
                        setEditingComment(commentId);
                        setEditContent(content);
                      }}
                      onDelete={(commentId) => {
                        if (window.confirm('Are you sure you want to delete this comment?')) {
                          deleteCommentMutation.mutate(commentId);
                        }
                      }}
                      onLike={(commentId) => likeCommentMutation.mutate(commentId)}
                      currentUserId={user?._id}
                      refreshTrigger={refreshTrigger}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Comment Input */}
        <div className="border-t p-4">
          {replyingTo && (
            <div className="mb-2 p-2 bg-gray-100 rounded text-sm">
              <span className="text-gray-600">
                Replying to {commentsData?.comments.find(c => c._id === replyingTo)?.user.displayName || 'comment'}
              </span>
              <button
                onClick={() => setReplyingTo(null)}
                className="ml-2 text-red-500 hover:text-red-700"
              >
                Cancel
              </button>
            </div>
          )}

          <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
              {user?.avatarUrl ? (
                <Image
                  src={user.avatarUrl}
                  alt={user.displayName || 'You'}
                  width={32}
                  height={32}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-primary text-white text-sm font-semibold">
                  {(user?.displayName || 'U').charAt(0).toUpperCase()}
                </div>
              )}
            </div>

            <div className="flex-1">
              <div className="relative">
                <textarea
                  ref={textareaRef}
                  value={newComment}
                  onChange={handleCommentChange}
                  placeholder="Add a comment..."
                  className="w-full p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
                  rows={3}
                />
                
                <div className="absolute bottom-2 right-2 flex gap-2">
                  <button
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className="p-1 text-gray-500 hover:text-primary transition-colors"
                  >
                    <BsEmojiSmile />
                  </button>
                </div>

                {/* Mention suggestions */}
                {showMentions && (
                  <div className="absolute bottom-12 left-0 z-20 bg-white border border-gray-200 rounded-lg shadow-lg p-2 w-48 max-h-32 overflow-y-auto">
                    {commentsData?.comments
                      .map(comment => comment.user)
                      .filter((commentUser, index, self) =>
                        self.findIndex(u => u._id === commentUser._id) === index && // Remove duplicates
                        commentUser._id !== user?._id && // Don't suggest current user
                        commentUser.username.toLowerCase().includes(mentionQuery.toLowerCase())
                      )
                      .slice(0, 5) // Limit to 5 suggestions
                      .map((commentUser) => (
                        <button
                          key={commentUser._id}
                          onClick={() => handleMentionSelect(commentUser.username)}
                          className="w-full text-left p-2 hover:bg-gray-100 rounded flex items-center gap-2"
                        >
                          <div className="w-6 h-6 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                            {commentUser.avatarUrl ? (
                              <Image
                                src={commentUser.avatarUrl}
                                alt={commentUser.displayName}
                                width={24}
                                height={24}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-primary text-white text-xs font-semibold">
                                {commentUser.displayName.charAt(0).toUpperCase()}
                              </div>
                            )}
                          </div>
                          <div>
                            <div className="text-sm font-medium">{commentUser.displayName}</div>
                            <div className="text-xs text-gray-500">@{commentUser.username}</div>
                          </div>
                        </button>
                      ))}
                    {commentsData?.comments
                      .map(comment => comment.user)
                      .filter((commentUser, index, self) =>
                        self.findIndex(u => u._id === commentUser._id) === index &&
                        commentUser._id !== user?._id &&
                        commentUser.username.toLowerCase().includes(mentionQuery.toLowerCase())
                      ).length === 0 && (
                      <div className="p-2 text-sm text-gray-500">No users found</div>
                    )}
                  </div>
                )}

                {showEmojiPicker && (
                  <div className="absolute bottom-12 right-0 z-10 bg-white border border-gray-200 rounded-lg shadow-lg p-2 w-56">
                    <div className="custom-emoji-picker grid grid-cols-8 gap-1">
                      {['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
                        '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
                        '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
                        '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
                        '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
                        '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
                        '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨',
                        '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
                        '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧',
                        '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐',
                        '🥴', '🤢', '🤮', '🤧', '😷', '🤒', '🤕', '🤑',
                        '🤠', '😈', '👿', '👹', '👺', '🤡', '💩', '👻',
                        '💀', '☠️', '👽', '👾', '🤖', '🎃', '😺', '😸',
                        '😹', '😻', '😼', '😽', '🙀', '😿', '😾', '❤️',
                        '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎',
                        '💔', '❣️', '💕', '💞', '💓', '💗', '💖', '💘',
                        '💝', '💟', '🔥', '✨', '💫', '⭐', '🌟', '💥',
                        '💯', '💢', '💨', '💦', '💤', '🎵', '🎶', '🎤',
                        '🎧', '🎼', '🎹', '🥁', '🎸', '🎺', '🎷', '🎻',
                        '🎪', '🎭', '🩰', '🎨', '🎬', '🎮', '🕹️', '🎯',
                        '🎲', '🧩', '🃏', '🀄', '🎴', '🎊', '🎉', '🎈',
                        '🎁', '🎀', '🛍️', '🎗️', '🏆', '🏅', '🥇', '🥈',
                        '🥉', '⚽', '🏀', '🏈', '⚾', '🥎', '🎾', '🏐',
                        '🏉', '🥏', '🎱', '🪀', '🏓', '🏸', '🏒', '🏑',
                        '🥍', '🏏', '🪃', '🥅', '⛳', '🪁', '🏹', '🎣',
                        '🤿', '🥊', '🥋', '🎽', '🛹', '🛼', '🛷', '⛸️',
                        '🥌', '🎿', '⛷️', '🏂', '🪂', '🏋️‍♀️', '🏋️', '🏋️‍♂️',
                        '🤼‍♀️', '🤼', '🤼‍♂️', '🤸‍♀️', '🤸', '🤸‍♂️', '⛹️‍♀️', '⛹️',
                        '⛹️‍♂️', '🤺', '🤾‍♀️', '🤾', '🤾‍♂️', '🏌️‍♀️', '🏌️', '🏌️‍♂️',
                        '🏇', '🧘‍♀️', '🧘', '🧘‍♂️', '🏄‍♀️', '🏄', '🏄‍♂️', '🏊‍♀️',
                        '🏊', '🏊‍♂️', '🤽‍♀️', '🤽', '🤽‍♂️', '🚣‍♀️', '🚣', '🚣‍♂️',
                        '🧗‍♀️', '🧗', '🧗‍♂️', '🚵‍♀️', '🚵', '🚵‍♂️', '🚴‍♀️', '🚴',
                        '🚴‍♂️', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️',
                        '🎗️', '🎫', '🎟️', '🎪', '🤹‍♀️', '🤹', '🤹‍♂️', '🎭',
                        '🩰', '🎨', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶',
                        '🥁', '🪘', '🎹', '🎸', '🪕', '🎻', '🎺', '🪗',
                        '🎷', '🪈', '🪇', '🔔', '🔕', '🎚️', '🎛️', '🎙️',
                        '📻', '📱', '📞', '☎️', '📟', '📠', '🔋', '🪫',
                        '🔌', '💻', '🖥️', '🖨️', '⌨️', '🖱️', '🖲️', '💽',
                        '💾', '💿', '📀', '🧮', '🎥', '🎞️', '📽️', '🎬',
                        '📺', '📷', '📸', '📹', '📼', '🔍', '🔎', '🕯️',
                        '💡', '🔦', '🏮', '🪔', '📔', '📕', '📖', '📗',
                        '📘', '📙', '📚', '📓', '📒', '📃', '📜', '📄',
                        '📰', '🗞️', '📑', '🔖', '🏷️', '💰', '🪙', '💴',
                        '💵', '💶', '💷', '💸', '💳', '🧾', '💹', '✉️',
                        '📧', '📨', '📩', '📤', '📥', '📦', '📫', '📪',
                        '📬', '📭', '📮', '🗳️', '✏️', '✒️', '🖋️', '🖊️',
                        '🖌️', '🖍️', '📝', '💼', '📁', '📂', '🗂️', '📅',
                        '📆', '🗒️', '🗓️', '📇', '📈', '📉', '📊', '📋',
                        '📌', '📍', '📎', '🖇️', '📏', '📐', '✂️', '🗃️',
                        '🗄️', '🗑️', '🔒', '🔓', '🔏', '🔐', '🔑', '🗝️',
                        '🔨', '🪓', '⛏️', '⚒️', '🛠️', '🗡️', '⚔️', '🔫',
                        '🪃', '🏹', '🛡️', '🪚', '🔧', '🪛', '🔩', '⚙️',
                        '🗜️', '⚖️', '🦯', '🔗', '⛓️', '🪝', '🧰', '🧲',
                        '🪜', '⚗️', '🧪', '🧫', '🧬', '🔬', '🔭', '📡'].map((emoji, index) => (
                        <button
                          key={index}
                          onClick={() => handleEmojiClick(emoji)}
                          className="p-1 hover:bg-gray-100 rounded text-sm transition-colors"
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-gray-500">
                  {newComment.length}/500 characters
                </span>
                <button
                  onClick={handleSubmitComment}
                  disabled={!newComment.trim() || createCommentMutation.isPending}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {createCommentMutation.isPending
                    ? (replyingTo ? 'Replying...' : 'Posting...')
                    : (replyingTo ? 'Post Reply' : 'Post Comment')
                  }
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
